<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/public/icons/openai.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src *;">
    <title>Switch.AI</title>
    <style>
      /* Prevent flash of unstyled content */
      body {
        margin: 0;
        padding: 0;
        font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #0a0a0a;
        color: #ffffff;
      }

      #root {
        width: 100vw;
        height: 100vh;
      }

      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100vw;
        height: 100vh;
        background-color: #0a0a0a;
        color: #ffffff;
        font-size: 18px;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">Loading Switch.AI...</div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
