import { useEffect } from 'react';
import { TopBar } from './components/TopBar';
import { Sidebar } from './components/Sidebar';
import { WebView } from './components/WebView';
import { Settings } from './pages/Settings';
import { useAppStore } from './store';
import { ErrorBoundary } from './components/ErrorBoundary';

export default function App() {
  const { activeMode } = useAppStore();

  useEffect(() => {
    console.log('App component mounted');
    console.log('Active mode:', activeMode);
    
    // Check if we're in Electron
    if (window.electronAPI) {
      console.log('Electron API available');
      
      // Get app info for debugging
      window.electronAPI.getAppInfo?.().then(info => {
        console.log('App info:', info);
      }).catch(err => {
        console.error('Failed to get app info:', err);
      });
    } else {
      console.warn('Electron API not available - running in browser mode');
    }

    // Add global error handling
    const handleError = (event: ErrorEvent) => {
      console.error('Global error:', event.error);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <ErrorBoundary>
      <div className="h-screen flex flex-col bg-bgPrimary overflow-hidden">
        <TopBar />
        <div className="flex-1 flex overflow-hidden">
          {activeMode === 'settings' ? (
            <div className="flex-1 overflow-y-auto">
              <Settings />
            </div>
          ) : (
            <>
              <Sidebar />
              <WebView />
            </>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}
