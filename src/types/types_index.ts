export interface Platform {
  id: string;
  mode: 'chat' | 'create' | 'lab' | 'hub';
  name: string;
  url: string;
  iconFile: string;
}

export interface ElectronAPI {
  createWebview: (options: { url: string; title: string; id: string }) => Promise<any>;
  openExternal: (url: string) => Promise<any>;
  webviewAction: (action: string, id: string, data?: any) => Promise<void>;
  onWebviewAction: (callback: (data: any) => void) => () => void;
  onWebviewReload: (callback: (data: any) => void) => () => void;
  getAppInfo: () => Promise<{
    version: string;
    isDev: boolean;
    platform: string;
    arch: string;
    electronVersion: string;
    chromeVersion: string;
    nodeVersion: string;
  }>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
