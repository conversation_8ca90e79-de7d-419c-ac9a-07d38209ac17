import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ errorInfo });
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  public render() {
    if (this.state.hasError) {
      return (
        <div className="h-screen flex items-center justify-center bg-bgPrimary text-textPrimary">
          <div className="text-center space-y-6 max-w-lg mx-auto px-6">
            <div className="w-20 h-20 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
              <span className="text-red-400 text-3xl">⚠</span>
            </div>
            
            <div className="space-y-3">
              <h1 className="text-3xl font-bold text-red-400">Something went wrong</h1>
              <p className="text-textSecondary">
                The application encountered an unexpected error. Please try reloading or resetting the app.
              </p>
            </div>

            {this.state.error && (
              <div className="bg-bgSecondary border border-border rounded-lg p-4 text-left">
                <h3 className="text-sm font-semibold text-textSecondary mb-2">Error Details:</h3>
                <pre className="text-xs text-red-400 whitespace-pre-wrap overflow-auto max-h-32">
                  {this.state.error.message}
                </pre>
                {this.state.errorInfo && (
                  <details className="mt-2">
                    <summary className="text-xs text-textTertiary cursor-pointer hover:text-textSecondary">
                      Stack Trace
                    </summary>
                    <pre className="text-xs text-textTertiary mt-2 whitespace-pre-wrap overflow-auto max-h-32">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            )}

            <div className="flex gap-3 justify-center">
              <button 
                onClick={this.handleReset}
                className="px-6 py-2 bg-accentStart hover:bg-accentHover text-white rounded-lg transition-colors font-medium"
              >
                Try Again
              </button>
              <button 
                onClick={this.handleReload}
                className="px-6 py-2 bg-bgSecondary hover:bg-bgTertiary text-textPrimary rounded-lg transition-colors font-medium border border-border"
              >
                Reload App
              </button>
            </div>

            <p className="text-xs text-textTertiary">
              If the problem persists, please restart the application.
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
