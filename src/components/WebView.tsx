import { useEffect, useRef, useState } from 'react';
import { useAppStore } from '../store';

const activeWebviews = new Set();

export const WebView = () => {
  const { activePlatform, activeMode } = useAppStore();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createEmbeddedWebview = async (platform: any) => {
    if (!containerRef.current || !window.electronAPI) return;
    console.log(`Creating embedded webview for ${platform.name}`);
    setIsLoading(true);
    setError(null);

    const webviewId = `webview-${platform.id}`;

    try {
      // Safely clear container and remove existing webviews
      // Use innerHTML = '' to avoid race conditions with removeChild
      const container = containerRef.current;
      container.innerHTML = '';

      // Create webview element
      const webview = document.createElement('webview') as any;
      webview.id = webviewId;
      webview.src = platform.url;
      // Apply critical CSS to ensure webview fills container, avoids overflow, and uses block display [[14]]
      // Explicitly set display:block and remove potentially conflicting autosize attribute
      webview.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        display: block; /* Explicitly set to block */
        overflow: hidden; /* Prevent scrollbars */
        background-color: #ffffff;
        min-height: 100%;
        min-width: 100%;
      `;

      // Set webview attributes
      webview.setAttribute('nodeintegration', 'false');
      webview.setAttribute('websecurity', 'false');
      webview.setAttribute('allowpopups', 'true');
      webview.setAttribute('plugins', 'true');
      // Removed 'autosize' attribute for more explicit control via CSS/container
      webview.setAttribute('useragent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      webview.setAttribute('preload', '');

      // Add comprehensive event listeners
      webview.addEventListener('dom-ready', () => {
        console.log(`Webview ${platform.id} DOM ready`);
        setIsLoading(false);

        // Log container dimensions for debugging
        if (containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          console.log(`Container dimensions: ${rect.width}x${rect.height}`);
        }

        // Inject CSS to improve the experience and potentially fix internal page layout
        webview.insertCSS(`
          html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
            min-height: 100vh !important;
            overflow-x: hidden !important; /* Prevent horizontal scrollbars */
            box-sizing: border-box !important;
          }

          /* Ensure main content containers fill the viewport */
          #root, [data-reactroot], .app, .main, .container {
            width: 100% !important;
            height: 100% !important;
            min-height: 100vh !important;
          }

          /* Hide potential overlays that might interfere */
          [data-testid="modal-backdrop"],
          .modal-backdrop,
          .overlay {
            z-index: 999999 !important;
          }
        `);
      });

      webview.addEventListener('did-start-loading', () => {
        console.log(`Webview ${platform.id} started loading`);
        setIsLoading(true);
      });

      webview.addEventListener('did-stop-loading', () => {
        console.log(`Webview ${platform.id} stopped loading`);
        setIsLoading(false);
      });

      webview.addEventListener('did-fail-load', (event: any) => {
        console.error(`Webview ${platform.id} failed to load:`, event);
        setIsLoading(false);
        setError(`Failed to load ${platform.name}: ${event.errorDescription || 'Unknown error'}`);
      });

      webview.addEventListener('did-finish-load', () => {
        console.log(`Webview ${platform.id} finished loading`);
        setIsLoading(false);
        setError(null);

        // Force a layout recalculation to ensure proper sizing
        setTimeout(() => {
          if (webview && webview.style) {
            webview.style.width = '100%';
            webview.style.height = '100%';
          }
        }, 100);
      });

      webview.addEventListener('new-window', (event: any) => {
        console.log('New window requested:', event.url);
        event.preventDefault();

        // Open external links in system browser
        if (window.electronAPI?.openExternal) {
          window.electronAPI.openExternal(event.url);
        } else {
          window.open(event.url, '_blank');
        }
      });

      webview.addEventListener('will-navigate', (event: any) => {
        console.log(`Webview ${platform.id} will navigate to:`, event.url);

        // Allow navigation within the same domain or trusted domains
        try {
          const currentDomain = new URL(platform.url).hostname;
          const targetDomain = new URL(event.url).hostname;

          if (targetDomain !== currentDomain) {
            console.log('Cross-domain navigation detected, opening externally');
            event.preventDefault();
            if (window.electronAPI?.openExternal) {
              window.electronAPI.openExternal(event.url);
            }
          }
        } catch (e) {
          console.warn('Could not parse URL for navigation check:', e);
        }
      });

      webview.addEventListener('console-message', (event: any) => {
        console.log(`Webview ${platform.id} console [${event.level}]:`, event.message);
      });

      webview.addEventListener('page-title-updated', (event: any) => {
        console.log(`Webview ${platform.id} title updated:`, event.title);
      });

      // Handle crashed webview
      webview.addEventListener('crashed', () => {
        console.error(`Webview ${platform.id} crashed`);
        setError(`${platform.name} crashed. Please try reloading.`);
        setIsLoading(false);
      });

      // Handle unresponsive webview
      webview.addEventListener('unresponsive', () => {
        console.warn(`Webview ${platform.id} became unresponsive`);
        setError(`${platform.name} is not responding.`);
      });

      webview.addEventListener('responsive', () => {
        console.log(`Webview ${platform.id} became responsive again`);
        setError(null);
      });

      // Add webview to container (already cleared above)
      container.appendChild(webview);

      activeWebviews.add(platform.id);
      console.log(`Created embedded webview for ${platform.name}`);

      // Call the electron API to register the webview
      await window.electronAPI.createWebview({
        url: platform.url,
        title: platform.name,
        id: platform.id
      });

    } catch (error) {
      console.error('Error creating webview:', error);
      setError(`Failed to create webview for ${platform.name}`);
      setIsLoading(false);
    }
  };

  const hideAllWebviews = () => {
    if (containerRef.current) {
      // Use innerHTML = '' for a more robust way to clear children
      // This avoids potential "The node to be removed is not a child" errors
      // that can occur with removeChild in dynamic scenarios during mode switches
      containerRef.current.innerHTML = '';
    }
    activeWebviews.clear();
    setIsLoading(false);
    setError(null);
  };

  const reloadCurrentWebview = () => {
    if (activePlatform && containerRef.current) {
      const webview = containerRef.current.querySelector(`#webview-${activePlatform.id}`) as any;
      if (webview && webview.reload) {
        webview.reload();
      }
    }
  };

  useEffect(() => {
    const manageWebviews = async () => {
      try {
        if (activeMode === 'settings' || !activePlatform) {
          hideAllWebviews();
          return;
        }
        if (!activeWebviews.has(activePlatform.id)) {
          await createEmbeddedWebview(activePlatform);
        }
      } catch (error) {
        console.error('Error managing webviews:', error);
        setError(`Failed to load ${activePlatform?.name || 'platform'}`);
      }
    };

    manageWebviews();
  }, [activePlatform, activeMode]);

  useEffect(() => {
    if (activeMode === 'settings') {
      hideAllWebviews();
    }
  }, [activeMode]);

  // Set up webview reload listener
  useEffect(() => {
    let cleanup: (() => void) | undefined;
    if (window.electronAPI?.onWebviewReload) {
      cleanup = window.electronAPI.onWebviewReload((data) => {
        if (data.id === activePlatform?.id) {
          reloadCurrentWebview();
        }
      });
    }

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [activePlatform]);

  // Handle container resize to ensure webview fills properly
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        console.log(`Container resized: ${width}x${height}`);

        // Force webview to update its size
        if (activePlatform && containerRef.current) {
          const webview = containerRef.current.querySelector(`#webview-${activePlatform.id}`) as any;
          if (webview) {
            // Trigger a layout update
            webview.style.width = '100%';
            webview.style.height = '100%';
          }
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [activePlatform]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up webviews when component unmounts
      hideAllWebviews();
    };
  }, []);

  const LoadingState = () => (
    <div className="absolute inset-0 flex items-center justify-center bg-bgPrimary z-10">
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-accentStart mb-2"></div>
        <p className="text-textSecondary">Loading {activePlatform?.name}...</p>
      </div>
    </div>
  );

  const ErrorState = () => (
    <div className="absolute inset-0 flex flex-col items-center justify-center bg-bgPrimary z-10 p-4">
      <div className="text-center mb-4">
        <div className="text-4xl mb-2">⚠️</div>
        <h3 className="text-xl font-bold text-error mb-1">Connection Error</h3>
        <p className="text-textSecondary text-sm">{error}</p>
      </div>
      <div className="flex gap-2">
        <button
          onClick={reloadCurrentWebview}
          className="px-4 py-2 bg-accentStart hover:bg-accentHover text-white rounded-lg transition-colors text-sm"
        >
          Reload
        </button>
        <button
          onClick={() => activePlatform && window.electronAPI?.openExternal(activePlatform.url)}
          className="px-4 py-2 bg-bgSecondary hover:bg-bgTertiary text-textPrimary rounded-lg transition-colors text-sm border border-border"
        >
          Open in Browser
        </button>
      </div>
    </div>
  );

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary h-full">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div ref={containerRef} className="flex-1 w-full relative">
      {!activePlatform && <EmptyState />}
      {isLoading && <LoadingState />}
      {error && <ErrorState />}
    </div>
  );
};