{"name": "switch-ai-electron", "private": true, "version": "0.1.0", "type": "module", "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently --kill-others \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite --host localhost --port 1420 --clearScreen false", "dev:electron": "wait-on http://localhost:1420 && NODE_ENV=development electron . --enable-logging", "build": "npm run build:renderer && npm run build:electron", "build:renderer": "tsc && vite build", "build:electron": "electron-builder", "preview": "vite preview", "postinstall": "electron-builder install-app-deps", "clean": "rm -rf dist dist-electron node_modules/.vite", "electron": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^38.0.0", "electron-builder": "^24.6.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.switchai.app", "productName": "Switch.AI", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "!electron/node_modules", "node_modules/**/*"], "extraResources": [{"from": "public", "to": "public"}], "mac": {"identity": null, "target": "dmg", "category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}