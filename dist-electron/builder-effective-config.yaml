directories:
  output: dist-electron
  buildResources: build
appId: com.switchai.app
productName: Switch.AI
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - '!electron/node_modules'
      - node_modules/**/*
extraResources:
  - from: public
    to: public
mac:
  identity: null
  target: dmg
  category: public.app-category.productivity
win:
  target: nsis
linux:
  target: AppImage
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 38.0.0
