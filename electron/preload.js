const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

console.log('Preload script loading...');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  createWebview: (options) => {
    console.log('Preload: createWebview called', options);
    return ipcRenderer.invoke('create-webview', options);
  },
  
  openExternal: (url) => {
    console.log('Preload: openExternal called', url);
    return ipcRenderer.invoke('open-external', url);
  },
  
  webviewAction: (action, id, data) => {
    console.log('Preload: webviewAction called', { action, id, data });
    return ipcRenderer.invoke('webview-action', { action, id, data });
  },
  
  onWebviewAction: (callback) => {
    const handler = (event, data) => callback(data);
    ipcRenderer.on('webview-action', handler);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('webview-action', handler);
    };
  },
  
  onWebviewReload: (callback) => {
    const handler = (event, data) => callback(data);
    ipcRenderer.on('webview-reload', handler);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('webview-reload', handler);
    };
  },
  
  getAppInfo: () => {
    return ipcRenderer.invoke('get-app-info');
  }
});

// Add global error handling
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Add window loaded event for debugging
window.addEventListener('DOMContentLoaded', () => {
  console.log('Preload: DOM Content Loaded');
});

console.log('Preload script loaded successfully');
