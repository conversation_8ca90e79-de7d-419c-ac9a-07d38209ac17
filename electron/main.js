import { app, BrowserWindow, shell, ipcMain, session } from 'electron';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add this for better debugging
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
const isDev = !app.isPackaged;

let mainWindow;

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false, // Disable for development
      allowRunningInsecureContent: true,
      webviewTag: true, // Enable webview tag support
      experimentalFeatures: true
    },
    titleBarStyle: 'default',
    show: false,
    icon: path.join(__dirname, '../public/icons/openai.svg')
  });

  // Enhanced loading logic
  if (isDev) {
    console.log('Development mode: Loading from localhost');
    mainWindow.loadURL('http://localhost:1420').catch(err => {
      console.error('Failed to load dev server:', err);
      // Fallback to file loading if dev server isn't running
      const indexPath = path.join(__dirname, '../dist/index.html');
      console.log('Fallback: Loading from file:', indexPath);
      mainWindow.loadFile(indexPath);
    });
  } else {
    const indexPath = path.join(__dirname, '../dist/index.html');
    console.log('Production mode: Loading from file:', indexPath);
    mainWindow.loadFile(indexPath);
  }

  // Enhanced error handling
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', {
      errorCode,
      errorDescription,
      validatedURL
    });
  });

  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('Renderer process crashed:', { killed });
  });

  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show');
    mainWindow.show();
    
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle webview console messages
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`Webview console [${level}]: ${message}`);
  });
}

app.whenReady().then(async () => {
  console.log('App ready, setting up...');
  
  // Remove default CSP to avoid loading issues
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src * \'unsafe-inline\' \'unsafe-eval\' data: blob:;']
      }
    });
  });

  // Additional session setup for webviews
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    details.requestHeaders['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    callback({ cancel: false, requestHeaders: details.requestHeaders });
  });

  // Enable webview permissions
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    console.log('Permission request:', permission);
    // Grant all permissions for webviews
    callback(true);
  });

  createMainWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Simplified IPC handlers for embedded webviews
ipcMain.handle('create-webview', async (event, { url, title, id }) => {
  console.log(`IPC: create-webview`, { url, title, id });
  return { success: true, id, url, title };
});

ipcMain.handle('open-external', async (event, url) => {
  console.log(`IPC: open-external`, url);
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    console.error('Error opening external URL:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('webview-action', async (event, { action, id, data }) => {
  console.log(`IPC: webview-action`, { action, id, data });
  
  switch (action) {
    case 'open-external':
      if (data?.url) {
        await shell.openExternal(data.url);
      }
      break;
    case 'reload':
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('webview-reload', { id });
      }
      break;
    default:
      console.log(`Unknown webview action: ${action}`);
  }
  
  return { success: true };
});

// Add app info for debugging
ipcMain.handle('get-app-info', async () => {
  return {
    version: app.getVersion(),
    isDev,
    platform: process.platform,
    arch: process.arch,
    electronVersion: process.versions.electron,
    chromeVersion: process.versions.chrome,
    nodeVersion: process.versions.node
  };
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  console.log('Certificate error:', error, 'for URL:', url);
  // In development, ignore certificate errors
  if (isDev) {
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});
